import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories/vector-store.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { generateS3Key, CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { ImageType } from '@shared/utils/file/image-media_type.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { PaginatedResult } from '@/common/response';
import { Agent, AgentTemplate } from '@modules/agent/entities';
import { AgentTemplateStatus, AgentStatusEnum } from '@modules/agent/constants';
import {
  AgentTemplateQueryDto,
  CreateAgentTemplateDto,
  UpdateAgentTemplateDto,
  UpdateAgentTemplateStatusDto,
  AgentTemplateListItemDto,
  AgentTemplateDetailDto,
  RestoreAgentTemplateDto,
  DeletedAgentTemplateQueryDto,
} from '../dto/agent-template';
import { TimeIntervalEnum } from '@utils/time';

/**
 * Service xử lý các thao tác liên quan đến mẫu agent cho admin
 */
@Injectable()
export class AdminAgentTemplateService {
  private readonly logger = new Logger(AdminAgentTemplateService.name);

  constructor(
    private readonly agentTemplateRepository: AgentTemplateRepository,
    private readonly agentRepository: AgentRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo mẫu agent mới
   * @param createDto Dữ liệu tạo mẫu agent
   * @param employeeId ID của nhân viên tạo
   * @returns ID của mẫu agent đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateAgentTemplateDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload: string | null }> {
    // Kiểm tra type agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(createDto.typeId);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    try {
      // Tạo agent trước
      const agentData: Partial<Agent> = {
        name: createDto.name,
        modelConfig: createDto.modelConfig,
        instruction: createDto.instruction,
      };

      const savedAgent = await this.agentRepository.save(agentData);

      // Tạo agent template
      const templateData: Partial<AgentTemplate> = {
        id: savedAgent.id,
        typeId: createDto.typeId,
        profile: createDto.profile || {},
        conversion: createDto.conversion || [],
        status: AgentTemplateStatus.DRAFT,
        modelSystemId: createDto.modelSystemId,
        memories: createDto.memories || [],
        createdBy: employeeId,
        updatedBy: employeeId,
      };

      await this.agentTemplateRepository.save(templateData);

      // Tạo URL upload cho avatar nếu có MIME type
      let avatarUrlUpload: string | null = null;
      if (createDto.avatarMimeType) {
        const avatarKey = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
          useTimeFolder: true,
        });

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(createDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );

        // Cập nhật avatar key cho agent
        await this.agentRepository.update(savedAgent.id, { avatar: avatarKey });
      }

      this.logger.log(`Created agent template with ID: ${savedAgent.id}`);
      return {
        id: savedAgent.id,
        avatarUrlUpload: avatarUrlUpload
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating agent template: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_CREATE_FAILED);
    }
  }

  /**
   * Lấy danh sách mẫu agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách mẫu agent với phân trang
   */
  async findAll(queryDto: AgentTemplateQueryDto): Promise<PaginatedResult<AgentTemplateListItemDto>> {
    try {
      const result = await this.agentTemplateRepository.findPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.status,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Map to DTO với dữ liệu đã được JOIN
      const items = await Promise.all(
        result.items.map(template => this.mapToListItemDtoOptimized(template))
      );

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      this.logger.error(`Error getting agent templates: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Lấy chi tiết mẫu agent theo ID
   * @param id ID của mẫu agent
   * @returns Chi tiết mẫu agent
   */
  async findById(id: string): Promise<AgentTemplateDetailDto> {
    const template = await this.agentTemplateRepository.findByIdWithDetails(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Kiểm tra template đã bị xóa mềm hay chưa
    if (template.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED);
    }

    try {
      return await this.mapToDetailDtoOptimized(template);
    } catch (error) {
      this.logger.error(`Error getting agent template detail: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Cập nhật mẫu agent
   * @param id ID của mẫu agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns ID của mẫu agent đã cập nhật
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentTemplateDto,
    employeeId: number,
  ): Promise<{ id: string, avatarUrlUpload?: string | null }> {
    // Kiểm tra template có tồn tại không
    const template = await this.agentTemplateRepository.findById(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Kiểm tra template đã bị xóa mềm hay chưa
    if (template.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED);
    }

    // Kiểm tra type agent nếu có cập nhật
    if (updateDto.typeId && updateDto.typeId !== template.typeId) {
      const typeAgent = await this.typeAgentRepository.findById(updateDto.typeId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }
    }

    try {
      // Cập nhật agent trước
      const agentUpdateData: Partial<Agent> = {};
      if (updateDto.name) agentUpdateData.name = updateDto.name;

      if (updateDto.modelConfig) agentUpdateData.modelConfig = updateDto.modelConfig;
      if (updateDto.instruction !== undefined) agentUpdateData.instruction = updateDto.instruction;


      if (Object.keys(agentUpdateData).length > 0) {
        await this.agentRepository.update(id, agentUpdateData);
      }

      // Cập nhật agent template
      const templateUpdateData: Partial<AgentTemplate> = {};
      if (updateDto.typeId) templateUpdateData.typeId = updateDto.typeId;
      if (updateDto.profile) templateUpdateData.profile = updateDto.profile;
      if (updateDto.convertConfig) templateUpdateData.convertConfig = updateDto.convertConfig;
      if (updateDto.conversion) templateUpdateData.conversion = updateDto.conversion;
      if (updateDto.modelBaseId !== undefined) templateUpdateData.modelBaseId = updateDto.modelBaseId;
      if (updateDto.modelSystemId !== undefined) templateUpdateData.modelSystemId = updateDto.modelSystemId;
      if (updateDto.memories) templateUpdateData.memories = updateDto.memories;
      if (updateDto.isForSale !== undefined) templateUpdateData.isForSale = updateDto.isForSale;
      templateUpdateData.updatedBy = employeeId;

      if (Object.keys(templateUpdateData).length > 1) { // > 1 vì luôn có updatedBy
        await this.agentTemplateRepository.update(id, templateUpdateData);
      }

      // Tạo URL upload cho avatar nếu có MIME type
      let avatarUrlUpload: string | null = null;
      if (updateDto.avatarMimeType) {
        const avatarKey = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
          useTimeFolder: true,
        });

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );

        // Cập nhật avatar key cho agent
        await this.agentRepository.update(id, { avatar: avatarKey });
      }

      this.logger.log(`Updated agent template with ID: ${id}`);
      return { id, avatarUrlUpload };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating agent template: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_UPDATE_FAILED);
    }
  }

  /**
   * Cập nhật trạng thái mẫu agent
   * @param id ID của mẫu agent
   * @param updateStatusDto Dữ liệu cập nhật trạng thái
   * @param employeeId ID của nhân viên cập nhật
   * @returns ID của mẫu agent đã cập nhật
   */
  @Transactional()
  async updateStatus(
    id: string,
    updateStatusDto: UpdateAgentTemplateStatusDto,
    employeeId: number,
  ): Promise<{ id: string }> {
    // Kiểm tra template có tồn tại không
    const template = await this.agentTemplateRepository.findById(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Kiểm tra template đã bị xóa mềm hay chưa
    if (template.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED);
    }

    try {
      await this.agentTemplateRepository.updateStatus(id, updateStatusDto.status, employeeId);

      this.logger.log(`Updated agent template status to ${updateStatusDto.status} for ID: ${id}`);
      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating agent template status: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_UPDATE_FAILED);
    }
  }

  /**
   * Xóa mềm mẫu agent
   * @param id ID của mẫu agent
   * @param employeeId ID của nhân viên xóa
   * @returns ID của mẫu agent đã xóa
   */
  @Transactional()
  async softDelete(id: string, employeeId: number): Promise<{ id: string }> {
    // Kiểm tra template có tồn tại không
    const template = await this.agentTemplateRepository.findById(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Kiểm tra template đã bị xóa mềm hay chưa
    if (template.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED);
    }

    try {
      // Xóa mềm agent template
      await this.agentTemplateRepository.update(id, {
        deletedBy: employeeId,
        updatedBy: employeeId,
      });

      // Xóa mềm agent
      await this.agentRepository.update(id, {
        deletedAt: Date.now(),
      });

      this.logger.log(`Soft deleted agent template with ID: ${id}`);
      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error soft deleting agent template: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách mẫu agent đã xóa với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách mẫu agent đã xóa với phân trang
   */
  async findDeleted(queryDto: DeletedAgentTemplateQueryDto): Promise<PaginatedResult<AgentTemplateListItemDto>> {
    try {
      const result = await this.agentTemplateRepository.findDeletedPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Map to DTO với dữ liệu đã được JOIN
      const items = await Promise.all(
        result.items.map(template => this.mapToListItemDtoOptimized(template))
      );

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      this.logger.error(`Error getting deleted agent templates: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Lấy chi tiết mẫu agent đã xóa theo ID
   * @param id ID của mẫu agent
   * @returns Chi tiết mẫu agent đã xóa
   */
  async findDeletedById(id: string): Promise<AgentTemplateDetailDto> {
    const template = await this.agentTemplateRepository.findDeletedByIdWithDetails(id);
    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    try {
      return await this.mapToDetailDtoOptimized(template);
    } catch (error) {
      this.logger.error(`Error getting deleted agent template detail: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Khôi phục mẫu agent đã xóa
   * @param restoreDto Dữ liệu khôi phục
   * @param employeeId ID của nhân viên khôi phục
   * @returns Số lượng mẫu agent đã khôi phục
   */
  @Transactional()
  async restore(
    restoreDto: RestoreAgentTemplateDto,
    employeeId: number,
  ): Promise<{ restoredCount: number }> {
    try {
      let restoredCount = 0;

      for (const id of restoreDto.ids) {
        // Kiểm tra template có tồn tại và đã bị xóa không
        const template = await this.agentTemplateRepository.findDeletedById(id);
        if (!template) {
          this.logger.warn(`Agent template with ID ${id} not found or not deleted`);
          continue;
        }

        // Khôi phục agent template
        const templateRestored = await this.agentTemplateRepository.restoreTemplate(id, employeeId);
        if (templateRestored > 0) {
          // Khôi phục agent
          await this.agentRepository.update(id, {
            deletedAt: null,
          });
          restoredCount++;
        }
      }

      this.logger.log(`Restored ${restoredCount} agent templates`);
      return { restoredCount };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error restoring agent templates: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_RESTORE_FAILED);
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Map AgentTemplate entity to list item DTO
   * @param template AgentTemplate entity
   * @returns AgentTemplateListItemDto
   */
  private async mapToListItemDto(template: AgentTemplate): Promise<AgentTemplateListItemDto> {
    // Lấy thông tin agent
    const agent = await this.agentRepository.findById(template.id);
    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Lấy thông tin type agent
    const typeAgent = await this.typeAgentRepository.findById(template.typeId);

    // Lấy thông tin nhân viên tạo
    const createdByInfo = template.createdBy
      ? await this.employeeInfoService.getEmployeeInfo(template.createdBy)
      : null;

    // Lấy thông tin nhân viên cập nhật
    const updatedByInfo = template.updatedBy
      ? await this.employeeInfoService.getEmployeeInfo(template.updatedBy)
      : null;

    const dto = new AgentTemplateListItemDto();
    dto.id = template.id;
    dto.name = agent.name;
    dto.avatar = agent.avatar ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_HOUR) : null;
    dto.typeId = template.typeId;
    dto.typeName = typeAgent?.name || 'Unknown Type';
    dto.status = template.status;
    dto.isForSale = template.isForSale;
    dto.createdBy = createdByInfo ? {
      employeeId: createdByInfo.id,
      name: createdByInfo.name,
      avatar: createdByInfo.avatar || '',
    } : null;
    dto.updatedBy = updatedByInfo ? {
      employeeId: updatedByInfo.id,
      name: updatedByInfo.name,
      avatar: updatedByInfo.avatar || '',
    } : null;
    dto.createdAt = new Date(agent.createdAt);
    dto.updatedAt = new Date(agent.updatedAt);

    return dto;
  }

  /**
   * Map AgentTemplate entity to detail DTO
   * @param template AgentTemplate entity
   * @returns AgentTemplateDetailDto
   */
  private async mapToDetailDto(template: AgentTemplate): Promise<AgentTemplateDetailDto> {
    // Lấy thông tin agent
    const agent = await this.agentRepository.findById(template.id);
    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Lấy thông tin type agent
    const typeAgent = await this.typeAgentRepository.findById(template.typeId);

    // Lấy thông tin nhân viên tạo
    const createdByInfo = template.createdBy
      ? await this.employeeInfoService.getEmployeeInfo(template.createdBy)
      : null;

    // Lấy thông tin nhân viên cập nhật
    const updatedByInfo = template.updatedBy
      ? await this.employeeInfoService.getEmployeeInfo(template.updatedBy)
      : null;

    const dto = new AgentTemplateDetailDto();
    dto.id = template.id;
    dto.name = agent.name;
    dto.avatar = agent.avatar ? await this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.modelConfig = agent.modelConfig;
    dto.instruction = agent.instruction || undefined;
    dto.profile = template.profile;
    dto.convertConfig = template.convertConfig;
    dto.conversion = template.conversion;
    dto.status = template.status;
    dto.modelBaseId = template.modelBaseId;
    dto.modelSystemId = template.modelSystemId;
    dto.memories = template.memories;
    dto.isForSale = template.isForSale;

    return dto;
  }

  /**
   * Map AgentTemplate entity với dữ liệu JOIN to list item DTO (tối ưu)
   * @param template AgentTemplate entity với dữ liệu JOIN
   * @returns AgentTemplateListItemDto
   */
  private async mapToListItemDtoOptimized(template: AgentTemplate & { agent: any; typeAgent: any }): Promise<AgentTemplateListItemDto> {
    // Lấy thông tin nhân viên tạo
    const createdByInfo = template.createdBy
      ? await this.employeeInfoService.getEmployeeInfo(template.createdBy)
      : null;

    // Lấy thông tin nhân viên cập nhật
    const updatedByInfo = template.updatedBy
      ? await this.employeeInfoService.getEmployeeInfo(template.updatedBy)
      : null;

    const dto = new AgentTemplateListItemDto();
    dto.id = template.id;
    dto.name = template.agent.name;
    dto.avatar = template.agent.avatar ? this.cdnService.generateUrlView(template.agent.avatar, TimeIntervalEnum.ONE_HOUR) : null;
    dto.typeId = template.typeId;
    dto.typeName = template.typeAgent?.name || 'Unknown Type';
    dto.status = template.status;
    dto.isForSale = template.isForSale;
    dto.createdBy = createdByInfo ? {
      employeeId: createdByInfo.id,
      name: createdByInfo.name,
      avatar: createdByInfo.avatar || '',
    } : null;
    dto.updatedBy = updatedByInfo ? {
      employeeId: updatedByInfo.id,
      name: updatedByInfo.name,
      avatar: updatedByInfo.avatar || '',
    } : null;
    dto.createdAt = template.agent.createdAt;
    dto.updatedAt = template.agent.updatedAt;

    return dto;
  }

  /**
   * Map AgentTemplate entity với dữ liệu JOIN to detail DTO (tối ưu)
   * @param template AgentTemplate entity với dữ liệu JOIN
   * @returns AgentTemplateDetailDto
   */
  private async mapToDetailDtoOptimized(template: AgentTemplate & { agent: any; typeAgent: any }): Promise<AgentTemplateDetailDto> {
    // Lấy thông tin nhân viên tạo
    const createdByInfo = template.createdBy
      ? await this.employeeInfoService.getEmployeeInfo(template.createdBy)
      : null;

    // Lấy thông tin nhân viên cập nhật
    const updatedByInfo = template.updatedBy
      ? await this.employeeInfoService.getEmployeeInfo(template.updatedBy)
      : null;

    const dto = new AgentTemplateDetailDto();
    dto.id = template.id;
    dto.name = template.agent.name;
    dto.avatar = template.agent.avatar ? this.cdnService.generateUrlView(template.agent.avatar, TimeIntervalEnum.ONE_HOUR) : null;
    dto.modelConfig = template.agent.modelConfig;
    dto.instruction = template.agent.instruction;
    dto.typeId = template.typeId;
    dto.typeName = template.typeAgent?.name || 'Unknown Type';
    dto.profile = template.profile;
    dto.convertConfig = template.convertConfig;
    dto.conversion = template.conversion;
    dto.status = template.status;
    dto.modelBaseId = template.modelBaseId;
    dto.modelSystemId = template.modelSystemId;
    dto.memories = template.memories;
    dto.isForSale = template.isForSale;
    dto.createdBy = createdByInfo ? {
      employeeId: createdByInfo.id,
      name: createdByInfo.name,
      avatar: createdByInfo.avatar || '',
    } : null;
    dto.updatedBy = updatedByInfo ? {
      employeeId: updatedByInfo.id,
      name: updatedByInfo.name,
      avatar: updatedByInfo.avatar || '',
    } : null;
    dto.createdAt = template.agent.createdAt;
    dto.updatedAt = template.agent.updatedAt;

    return dto;
  }
}