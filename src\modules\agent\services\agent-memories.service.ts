import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/dto';
import { AgentMemoriesRepository } from '../repositories/agent-memories.repository';
import { AgentRepository } from '../repositories/agent.repository';
import { AgentMemories } from '../entities/agent-memories.entity';
import {
  CreateAgentMemoryDto,
  UpdateAgentMemoryDto,
  AgentMemoriesQueryDto,
  AgentMemoryResponseDto,
  MemoryTypeEnum,
} from '../dto/agent-memories.dto';
import { AGENT_ERROR_CODES } from '../exceptions/agent.exception';

/**
 * Service xử lý logic nghiệp vụ cho Agent Memories
 */
@Injectable()
export class AgentMemoriesService {
  private readonly logger = new Logger(AgentMemoriesService.name);

  constructor(
    private readonly agentMemoriesRepository: AgentMemoriesRepository,
    private readonly agentRepository: AgentRepository,
  ) {}

  /**
   * Tạo memory mới cho agent
   * @param dto Dữ liệu tạo memory
   * @returns Memory đã được tạo
   */
  @Transactional()
  async createMemory(dto: CreateAgentMemoryDto): Promise<AgentMemoryResponseDto> {
    this.logger.log(`Tạo memory mới cho agent: ${dto.agentId}`);

    // Kiểm tra agent có tồn tại không
    const agentExists = await this.agentRepository.existsById(dto.agentId);
    if (!agentExists) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Agent với ID ${dto.agentId} không tồn tại`
      );
    }

    try {
      // Tạo memory entity
      const memory = this.agentMemoriesRepository.create({
        agentId: dto.agentId,
        structuredContent: dto.structuredContent,
        metadata: dto.metadata,
      });

      // Lưu vào database
      const savedMemory = await this.agentMemoriesRepository.save(memory);

      this.logger.log(`Đã tạo memory thành công với ID: ${savedMemory.id}`);
      return this.mapToResponseDto(savedMemory);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo memory: ${error.message}`, error.stack);
      throw new AppException(
        AGENT_ERROR_CODES.MEMORY_CREATE_FAILED,
        `Không thể tạo memory: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách memories của agent với phân trang
   * @param queryDto Query parameters
   * @returns Danh sách memories với phân trang
   */
  async getMemories(queryDto: AgentMemoriesQueryDto): Promise<PaginatedResult<AgentMemoryResponseDto>> {
    this.logger.log(`Lấy danh sách memories với query: ${JSON.stringify(queryDto)}`);

    try {
      let memories: PaginatedResult<AgentMemories>;

      if (queryDto.agentId) {
        // Lấy memories của agent cụ thể
        memories = await this.agentMemoriesRepository.findByAgentIdWithPagination(
          queryDto.agentId,
          queryDto.page,
          queryDto.limit
        );
      } else {
        // Lấy tất cả memories (có thể cần quyền admin)
        throw new AppException(
          AGENT_ERROR_CODES.INVALID_QUERY,
          'Agent ID là bắt buộc để lấy danh sách memories'
        );
      }

      // Áp dụng các filter khác
      let filteredItems = memories.items;

      if (queryDto.type) {
        filteredItems = filteredItems.filter(
          memory => memory.structuredContent.type === queryDto.type
        );
      }

      if (queryDto.minImportance) {
        filteredItems = filteredItems.filter(
          memory => (memory.structuredContent.importance || 0) >= queryDto.minImportance!
        );
      }

      if (queryDto.tags) {
        const searchTags = queryDto.tags.split(',').map(tag => tag.trim().toLowerCase());
        filteredItems = filteredItems.filter(memory => {
          const memoryTags = memory.metadata?.tags || [];
          return searchTags.some(searchTag =>
            memoryTags.some(tag => tag.toLowerCase().includes(searchTag))
          );
        });
      }

      if (queryDto.status) {
        filteredItems = filteredItems.filter(
          memory => memory.metadata?.status === queryDto.status
        );
      }

      // Áp dụng search nếu có
      if (queryDto.search) {
        const searchTerm = queryDto.search.toLowerCase();
        filteredItems = filteredItems.filter(memory =>
          JSON.stringify(memory.structuredContent).toLowerCase().includes(searchTerm) ||
          JSON.stringify(memory.metadata || {}).toLowerCase().includes(searchTerm)
        );
      }

      const responseItems = filteredItems.map(memory => this.mapToResponseDto(memory));

      return {
        ...memories,
        items: responseItems,
        total: filteredItems.length,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách memories: ${error.message}`, error.stack);
      throw new AppException(
        AGENT_ERROR_CODES.MEMORY_FETCH_FAILED,
        `Không thể lấy danh sách memories: ${error.message}`
      );
    }
  }

  /**
   * Lấy memory theo ID
   * @param memoryId UUID của memory
   * @returns Memory details
   */
  async getMemoryById(memoryId: string): Promise<AgentMemoryResponseDto> {
    this.logger.log(`Lấy memory với ID: ${memoryId}`);

    try {
      const memory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId },
        relations: ['agent'],
      });

      if (!memory) {
        throw new AppException(
          AGENT_ERROR_CODES.MEMORY_NOT_FOUND,
          `Memory với ID ${memoryId} không tồn tại`
        );
      }

      return this.mapToResponseDto(memory);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy memory: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.MEMORY_FETCH_FAILED,
        `Không thể lấy memory: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật memory
   * @param memoryId UUID của memory
   * @param dto Dữ liệu cập nhật
   * @returns Memory đã được cập nhật
   */
  @Transactional()
  async updateMemory(memoryId: string, dto: UpdateAgentMemoryDto): Promise<AgentMemoryResponseDto> {
    this.logger.log(`Cập nhật memory với ID: ${memoryId}`);

    try {
      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId },
      });

      if (!existingMemory) {
        throw new AppException(
          AGENT_ERROR_CODES.MEMORY_NOT_FOUND,
          `Memory với ID ${memoryId} không tồn tại`
        );
      }

      // Cập nhật memory
      const updateData: Partial<AgentMemories> = {};

      if (dto.structuredContent) {
        updateData.structuredContent = {
          ...existingMemory.structuredContent,
          ...dto.structuredContent,
        };
      }

      if (dto.metadata) {
        updateData.metadata = {
          ...existingMemory.metadata,
          ...dto.metadata,
          last_updated: Date.now(),
        };
      }

      await this.agentMemoriesRepository.update(memoryId, updateData);

      // Lấy memory đã được cập nhật
      const updatedMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId },
        relations: ['agent'],
      });

      this.logger.log(`Đã cập nhật memory thành công với ID: ${memoryId}`);
      return this.mapToResponseDto(updatedMemory!);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật memory: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.MEMORY_UPDATE_FAILED,
        `Không thể cập nhật memory: ${error.message}`
      );
    }
  }

  /**
   * Xóa memory
   * @param memoryId UUID của memory
   */
  @Transactional()
  async deleteMemory(memoryId: string): Promise<void> {
    this.logger.log(`Xóa memory với ID: ${memoryId}`);

    try {
      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId },
      });

      if (!existingMemory) {
        throw new AppException(
          AGENT_ERROR_CODES.MEMORY_NOT_FOUND,
          `Memory với ID ${memoryId} không tồn tại`
        );
      }

      // Xóa memory
      await this.agentMemoriesRepository.delete(memoryId);

      this.logger.log(`Đã xóa memory thành công với ID: ${memoryId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa memory: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.MEMORY_DELETE_FAILED,
        `Không thể xóa memory: ${error.message}`
      );
    }
  }

  /**
   * Tìm kiếm memories theo nội dung
   * @param agentId UUID của agent
   * @param searchTerm Từ khóa tìm kiếm
   * @returns Danh sách memories phù hợp
   */
  async searchMemories(agentId: string, searchTerm: string): Promise<AgentMemoryResponseDto[]> {
    this.logger.log(`Tìm kiếm memories cho agent ${agentId} với từ khóa: ${searchTerm}`);

    try {
      const memories = await this.agentMemoriesRepository.searchByContent(agentId, searchTerm);
      return memories.map(memory => this.mapToResponseDto(memory));
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm memories: ${error.message}`, error.stack);
      throw new AppException(
        AGENT_ERROR_CODES.MEMORY_SEARCH_FAILED,
        `Không thể tìm kiếm memories: ${error.message}`
      );
    }
  }

  /**
   * Tăng usage count của memory
   * @param memoryId UUID của memory
   * @returns Memory đã được cập nhật
   */
  @Transactional()
  async incrementUsageCount(memoryId: string): Promise<AgentMemoryResponseDto> {
    this.logger.log(`Tăng usage count cho memory: ${memoryId}`);

    try {
      const updatedMemory = await this.agentMemoriesRepository.incrementUsageCount(memoryId);

      if (!updatedMemory) {
        throw new AppException(
          AGENT_ERROR_CODES.MEMORY_NOT_FOUND,
          `Memory với ID ${memoryId} không tồn tại`
        );
      }

      return this.mapToResponseDto(updatedMemory);
    } catch (error) {
      this.logger.error(`Lỗi khi tăng usage count: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.MEMORY_UPDATE_FAILED,
        `Không thể cập nhật usage count: ${error.message}`
      );
    }
  }

  /**
   * Map entity sang response DTO
   * @param memory AgentMemories entity
   * @returns AgentMemoryResponseDto
   */
  private mapToResponseDto(memory: AgentMemories): AgentMemoryResponseDto {
    return {
      id: memory.id,
      agentId: memory.agentId,
      structuredContent: memory.structuredContent,
      metadata: memory.metadata,
      createdAt: memory.createdAt,
    };
  }
}
