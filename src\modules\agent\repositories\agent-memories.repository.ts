import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { PaginatedResult } from '@common/dto';
import { AgentMemories } from '../entities/agent-memories.entity';

/**
 * Repository cho AgentMemories entity
 * Xử lý các thao tác database liên quan đến agent memories
 */
@Injectable()
export class AgentMemoriesRepository extends Repository<AgentMemories> {
  constructor(private dataSource: DataSource) {
    super(AgentMemories, dataSource.createEntityManager());
  }

  /**
   * Tạo base query với các join cần thiết
   * @returns QueryBuilder cơ bản
   */
  private createBaseQuery(): SelectQueryBuilder<AgentMemories> {
    return this.createQueryBuilder('agentMemories')
      .leftJoinAndSelect('agentMemories.agent', 'agent')
      .where('agentMemories.id IS NOT NULL');
  }

  /**
   * Tìm memories theo agent ID
   * @param agentId UUID của agent
   * @returns Danh sách memories của agent
   */
  async findByAgentId(agentId: string): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm memories theo agent ID với phân trang
   * @param agentId UUID của agent
   * @param page Trang hiện tại
   * @param limit Số lượng items per page
   * @returns Kết quả phân trang
   */
  async findByAgentIdWithPagination(
    agentId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResult<AgentMemories>> {
    const query = this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .orderBy('agentMemories.createdAt', 'DESC');

    const [items, total] = await query
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      hasNextPage: page < Math.ceil(total / limit),
      hasPreviousPage: page > 1,
    };
  }

  /**
   * Tìm kiếm memories theo nội dung
   * @param agentId UUID của agent
   * @param searchTerm Từ khóa tìm kiếm
   * @returns Danh sách memories phù hợp
   */
  async searchByContent(agentId: string, searchTerm: string): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere(
        '(agentMemories.structuredContent::text ILIKE :searchTerm OR agentMemories.metadata::text ILIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` }
      )
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm memories theo loại (type trong structured_content)
   * @param agentId UUID của agent
   * @param type Loại memory (skill, knowledge, personality, etc.)
   * @returns Danh sách memories theo loại
   */
  async findByType(agentId: string, type: string): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere("agentMemories.structuredContent->>'type' = :type", { type })
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm memories theo tags trong metadata
   * @param agentId UUID của agent
   * @param tags Danh sách tags
   * @returns Danh sách memories có chứa tags
   */
  async findByTags(agentId: string, tags: string[]): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere('agentMemories.metadata->\'tags\' ?| array[:...tags]', { tags })
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm memories theo mức độ quan trọng
   * @param agentId UUID của agent
   * @param minImportance Mức độ quan trọng tối thiểu
   * @returns Danh sách memories có mức độ quan trọng >= minImportance
   */
  async findByImportance(agentId: string, minImportance: number): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere(
        '(agentMemories.structuredContent->>\'importance\')::numeric >= :minImportance',
        { minImportance }
      )
      .orderBy('(agentMemories.structuredContent->>\'importance\')::numeric', 'DESC')
      .addOrderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Đếm số lượng memories theo agent
   * @param agentId UUID của agent
   * @returns Số lượng memories
   */
  async countByAgentId(agentId: string): Promise<number> {
    return this.createQueryBuilder('agentMemories')
      .where('agentMemories.agentId = :agentId', { agentId })
      .getCount();
  }

  /**
   * Xóa tất cả memories của một agent
   * @param agentId UUID của agent
   * @returns Số lượng memories đã xóa
   */
  async deleteByAgentId(agentId: string): Promise<number> {
    const result = await this.createQueryBuilder('agentMemories')
      .delete()
      .where('agentId = :agentId', { agentId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Cập nhật metadata của memory
   * @param memoryId UUID của memory
   * @param metadata Metadata mới
   * @returns Memory đã được cập nhật
   */
  async updateMetadata(memoryId: string, metadata: any): Promise<AgentMemories | null> {
    await this.createQueryBuilder('agentMemories')
      .update()
      .set({ metadata })
      .where('id = :memoryId', { memoryId })
      .execute();

    return this.findOne({ where: { id: memoryId } });
  }

  /**
   * Tăng usage_count trong metadata
   * @param memoryId UUID của memory
   * @returns Memory đã được cập nhật
   */
  async incrementUsageCount(memoryId: string): Promise<AgentMemories | null> {
    await this.createQueryBuilder('agentMemories')
      .update()
      .set({
        metadata: () => `
          CASE 
            WHEN metadata IS NULL THEN '{"usage_count": 1}'::jsonb
            WHEN metadata->>'usage_count' IS NULL THEN metadata || '{"usage_count": 1}'::jsonb
            ELSE metadata || jsonb_build_object('usage_count', (metadata->>'usage_count')::int + 1)
          END
        `
      })
      .where('id = :memoryId', { memoryId })
      .execute();

    return this.findOne({ where: { id: memoryId } });
  }
}
