import { Entity, PrimaryGeneratedColumn, Column, Index, ManyToOne, JoinColumn } from 'typeorm';
import { Agent } from './agent.entity';
import { StructuredContentInterface } from '../interfaces/agent-memory.interface';


/**
 * Entity đại diện cho bảng agent_memories trong cơ sở dữ liệu
 * Lưu trữ kiến thức, k<PERSON> năng, hoặc tính cách riêng của từng agent
 */
@Entity('agent_memories')
@Index('idx_agent_memories_agent_id', ['agentId'])
export class AgentMemories {
  /**
   * UUID của memory, sinh tự động
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * UUID của agent sở hữu memory này
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: false })
  agentId: string;

  /**
   * Nội dung kiến thức dưới dạng JSON
   * Ví dụ: skill_name, description, examples
   */
  @Column({
    name: 'structured_content',
    type: 'jsonb',
    nullable: false,
    comment: 'Nội dung kiến thức dưới dạng JSON (ví dụ: skill_name, description, examples)'
  })
  structuredContent: StructuredContentInterface;

  /**
   * Thông tin metadata bổ sung
   */
  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin metadata bổ sung cho memory'
  })
  metadata?: Record<string, any>;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: true,
    comment: 'Thời gian tạo memory'
  })
  createdAt?: number;

  /**
   * Quan hệ Many-to-One với Agent
   * Một agent có thể có nhiều memories
   */
  @ManyToOne(() => Agent, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'agent_id' })
  agent?: Agent;
}
